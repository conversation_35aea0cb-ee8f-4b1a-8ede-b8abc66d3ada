# AI优化任务管理系统使用指南

## 概述

AI优化任务管理系统是一个独立的线程池任务管理器，用于在后台异步执行会议文本的AI优化工作。该系统支持任务重启恢复、并发控制、进度监控等功能。

## 系统架构

### 核心组件

1. **RefineTaskManager** (`app/tasks/refine_manager.py`)
   - 独立线程池任务管理器
   - 支持最大并发数控制（默认5个）
   - 自动任务恢复和清理
   - 线程安全的状态管理

2. **新增API接口** (`app/routers/meeting.py`)
   - `POST /v1/api/meeting/refine-text/{meeting_id}` - 启动AI优化任务
   - `GET /v1/api/meeting/refine-status/{meeting_id}` - 查询任务状态
   - `POST /v1/api/meeting/refine-cancel/{meeting_id}` - 取消任务
   - `GET /v1/api/meeting/refine-manager-status` - 管理器状态（调试用）

3. **数据库状态管理**
   - `meeting.status` 字段：`created` → `texted` → `refining` → `refined`
   - 应用重启时自动恢复 `refining` 状态的任务

## API接口详情

### 1. 启动AI优化任务

```http
POST /v1/api/meeting/refine-text/{meeting_id}
Authorization: Bearer {token}
```

**响应：**
```json
{
  "code": 200,
  "message": "",
  "data": {
    "task_id": 123,
    "meeting_id": 123,
    "status": "submitted",
    "message": "AI优化任务已提交，正在后台处理"
  }
}
```

**前置条件：**
- 会议的 `audioState` 必须为 `finished`
- 会议必须有 `textUrl`（转写结果）
- 会议状态不能是 `refining` 或 `refined`

### 2. 查询任务状态

```http
GET /v1/api/meeting/refine-status/{meeting_id}
Authorization: Bearer {token}
```

**响应：**
```json
{
  "code": 200,
  "message": "",
  "data": {
    "meeting_id": 123,
    "status": "processing",
    "progress": 65,
    "current_group": 13,
    "total_groups": 20,
    "started_at": "2024-01-01T10:00:00",
    "completed_at": null,
    "failed_at": null,
    "refined_url": null,
    "error": null
  }
}
```

**状态说明：**
- `not_started` - 未开始
- `starting` - 正在启动
- `running` - 正在运行
- `processing` - 正在处理分组
- `completed` - 已完成
- `failed` - 失败
- `canceled` - 已取消
- `pending` - 排队等待（重启后恢复）

### 3. 取消任务

```http
POST /v1/api/meeting/refine-cancel/{meeting_id}
Authorization: Bearer {token}
```

**响应：**
```json
{
  "code": 200,
  "message": "",
  "data": "AI优化任务已取消"
}
```

## 使用流程

### 前端集成示例

```javascript
// 1. 启动AI优化任务
async function startRefineTask(meetingId) {
  const response = await fetch(`/v1/api/meeting/refine-text/${meetingId}`, {
    method: 'POST',
    headers: {
      'Authorization': `Bearer ${token}`
    }
  });
  
  const result = await response.json();
  if (result.code === 200) {
    // 任务已提交，开始轮询状态
    pollTaskStatus(meetingId);
  }
}

// 2. 轮询任务状态
async function pollTaskStatus(meetingId) {
  const interval = setInterval(async () => {
    try {
      const response = await fetch(`/v1/api/meeting/refine-status/${meetingId}`, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });
      
      const result = await response.json();
      const status = result.data;
      
      // 更新UI进度
      updateProgress(status.progress, status.status);
      
      // 检查是否完成
      if (status.status === 'completed') {
        clearInterval(interval);
        onTaskCompleted(status.refined_url);
      } else if (status.status === 'failed') {
        clearInterval(interval);
        onTaskFailed(status.error);
      }
      
    } catch (error) {
      console.error('查询状态失败:', error);
    }
  }, 2000); // 每2秒查询一次
}

// 3. 取消任务
async function cancelTask(meetingId) {
  const response = await fetch(`/v1/api/meeting/refine-cancel/${meetingId}`, {
    method: 'POST',
    headers: {
      'Authorization': `Bearer ${token}`
    }
  });
  
  const result = await response.json();
  console.log(result.data); // "AI优化任务已取消"
}
```

## 系统特性

### 1. 任务重启恢复
- 应用重启时自动扫描数据库中 `status="refining"` 的任务
- 自动恢复未完成的任务继续执行

### 2. 并发控制
- 最大并发任务数：5个（可配置）
- 超出并发数的任务会返回提交失败
- 支持任务排队机制

### 3. 容错处理
- 单个组处理失败时使用原始数据作为fallback
- 网络异常时自动重试
- 详细的错误日志记录

### 4. 进度监控
- 实时进度反馈（0-100%）
- 当前处理组/总组数显示
- 任务开始、完成、失败时间记录

### 5. 线程安全
- 使用线程锁保护共享状态
- 安全的任务添加和移除操作

## 配置参数

在 `RefineTaskManager` 中可以调整的参数：

```python
class RefineTaskManager:
    def __init__(self):
        self.max_workers = 5  # 最大并发任务数
        # ... 其他配置
```

## 监控和调试

### 查看管理器状态
```http
GET /v1/api/meeting/refine-manager-status
Authorization: Bearer {token}
```

**响应：**
```json
{
  "code": 200,
  "message": "",
  "data": {
    "is_running": true,
    "running_tasks_count": 3,
    "max_workers": 5,
    "running_task_ids": [123, 456, 789],
    "task_status_count": 5
  }
}
```

### 日志查看
系统会输出详细的日志信息：
- 任务提交、开始、完成、失败日志
- 进度更新日志
- 错误详情日志

## 注意事项

1. **资源消耗：** AI优化任务会消耗较多CPU和内存，建议根据服务器配置调整并发数
2. **网络稳定性：** 确保与AI服务的网络连接稳定
3. **存储空间：** 优化后的结果会上传到OSS，确保有足够存储空间
4. **任务持久化：** 任务状态保存在内存中，重启后会丢失进度信息（但会重新开始）

## 错误处理

常见错误及解决方案：

| 错误 | 原因 | 解决方案 |
|------|------|----------|
| "转写任务尚未完成" | audioState != "finished" | 等待转写完成 |
| "AI优化任务正在执行中" | 重复提交 | 查询状态或取消后重新提交 |
| "任务提交失败，可能已达到最大并发数" | 并发数限制 | 等待其他任务完成或增加max_workers |
| "AI返回结果格式错误" | AI服务异常 | 检查AI服务状态，任务会自动fallback | 