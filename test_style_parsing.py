#!/usr/bin/env python3
"""
测试样式解析功能
"""
from bs4 import BeautifulSoup

def test_style_parsing():
    """测试样式解析"""
    # 测试样式字符串
    style_xml = '<w:pPr><w:pStyle w:val="5"/><w:bidi w:val="0"/><w:rPr><w:rFonts w:hint="default"/><w:lang w:eastAsia="zh-CN" w:val="en-US"/></w:rPr></w:pPr>'
    
    print("测试样式解析...")
    print(f"原始样式: {style_xml}")
    
    try:
        # 解析样式XML片段
        p_pr_soup = BeautifulSoup(style_xml, 'xml')
        print(f"解析后的soup: {p_pr_soup}")
        
        # 查找w:pPr元素
        p_pr = p_pr_soup.find('w:pPr')
        print(f"find('w:pPr')结果: {p_pr}")
        
        # 如果find失败，尝试获取第一个子元素
        if not p_pr:
            print("find失败，尝试获取第一个子元素...")
            if p_pr_soup.contents:
                p_pr = p_pr_soup.contents[0]
                print(f"第一个子元素: {p_pr}")
                print(f"元素名称: {getattr(p_pr, 'name', 'None')}")
        
        if p_pr and hasattr(p_pr, 'name') and p_pr.name == 'w:pPr':
            print("✅ 成功找到w:pPr元素")
            print(f"子元素数量: {len(list(p_pr.children))}")
            for i, child in enumerate(p_pr.children):
                if hasattr(child, 'name'):
                    print(f"  子元素{i}: {child.name} - {child.attrs}")
        else:
            print("❌ 未找到w:pPr元素")
            
    except Exception as e:
        print(f"❌ 解析失败: {e}")

if __name__ == "__main__":
    test_style_parsing()
