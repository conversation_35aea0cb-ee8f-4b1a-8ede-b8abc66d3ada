import threading
import time
import logging
import json
from datetime import datetime
from sqlalchemy.orm import Session
import os

# 设置日志
logger = logging.getLogger(__name__)

class TaskMonitor:
    """任务监控器 - 后台线程轮询监控 Celery 任务状态"""
    
    def __init__(self, poll_interval: int = None):
        """
        初始化任务监控器
        
        Args:
            poll_interval: 轮询间隔时间（秒），默认从环境变量读取或30秒
        """
        self.poll_interval = poll_interval or int(os.getenv("TASK_MONITOR_INTERVAL", 30))
        self.running = False
        self.monitor_thread = None
        # 统计信息
        self.total_checks = 0
        self.total_updates = 0
        
    def start(self):
        """启动任务监控"""
        if self.running:
            logger.warning("任务监控器已在运行中")
            return
            
        self.running = True
        self.monitor_thread = threading.Thread(target=self._monitor_loop, daemon=True)
        self.monitor_thread.start()
        logger.info(f"✅ 任务监控器已启动，轮询间隔: {self.poll_interval}秒")
        
    def stop(self):
        """停止任务监控"""
        if not self.running:
            return
            
        self.running = False
        if self.monitor_thread and self.monitor_thread.is_alive():
            self.monitor_thread.join(timeout=5)
        logger.info(f"🛑 任务监控器已停止，总检查次数: {self.total_checks}，总更新次数: {self.total_updates}")
        
    def get_stats(self):
        """获取监控统计信息"""
        return {
            "running": self.running,
            "poll_interval": self.poll_interval,
            "total_checks": self.total_checks,
            "total_updates": self.total_updates
        }
        
    def _monitor_loop(self):
        """监控循环 - 在后台线程中运行"""
        logger.info("🔍 任务监控循环已开始")
        
        while self.running:
            try:
                self.total_checks += 1
                updated_count = self._check_pending_tasks()
                self.total_updates += updated_count
                
                # 每100次检查输出一次统计信息
                if self.total_checks % 100 == 0:
                    logger.info(f"📊 监控统计: 检查次数={self.total_checks}, 更新次数={self.total_updates}")
                    
            except Exception as e:
                logger.error(f"监控任务时发生错误: {str(e)}")
            
            # 休眠指定间隔时间
            for _ in range(self.poll_interval):
                if not self.running:
                    break
                time.sleep(1)
        
        logger.info("🔍 任务监控循环已结束")
    
    def _check_pending_tasks(self):
        """检查待处理的任务状态，返回更新的任务数量"""
        updated_count = 0
        db = None
        
        try:
            from app.mysql import get_db
            from app.models import Meeting
            from app.tasks import celery_app
            from celery.result import AsyncResult
            
            # 获取数据库会话
            db = next(get_db())
            
            # 查询状态为 pending 或 running 的任务
            pending_meetings = db.query(Meeting).filter(
                Meeting.celeryTaskStatus.in_(['pending', 'running'])
            ).all()
            
            if len(pending_meetings) > 0:
                logger.info(f"检查到 {len(pending_meetings)} 个待处理任务")
            
            for meeting in pending_meetings:
                if meeting.celeryTaskId:
                    try:
                        # 获取 Celery 任务结果
                        task_result = AsyncResult(meeting.celeryTaskId, app=celery_app)
                        
                        # 检查任务是否已完成
                        if task_result.ready():
                            if task_result.successful():
                                # 任务成功
                                self._update_task_status_in_db(
                                    db, meeting.celeryTaskId, 'SUCCESS', task_result.result
                                )
                                updated_count += 1
                                logger.info(f"任务 {meeting.celeryTaskId} 成功完成，会议ID: {meeting.id}")
                            else:
                                # 任务失败
                                error_info = str(task_result.info) if task_result.info else "未知错误"
                                self._update_task_status_in_db(
                                    db, meeting.celeryTaskId, 'FAILURE', error_info
                                )
                                updated_count += 1
                                logger.warning(f"任务 {meeting.celeryTaskId} 执行失败，会议ID: {meeting.id}, 错误: {error_info}")
                        else:
                            print("="*20)
                            print(meeting.name)
                            print(task_result.state)
                            print("="*20)

                            # 任务还在运行，检查状态变化
                            current_state = task_result.state
                            if current_state == 'STARTED' and meeting.celeryTaskStatus != 'running':
                                # 任务从 pending 变为 running
                                self._update_task_status_in_db(
                                    db, meeting.celeryTaskId, 'STARTED'
                                )
                                updated_count += 1
                                logger.info(f"任务 {meeting.celeryTaskId} 开始执行，会议ID: {meeting.id}")
                            elif current_state == 'PROGRESS':
                                # 更新进度信息
                                progress_info = task_result.info if task_result.info else {}
                                self._update_task_status_in_db(
                                    db, meeting.celeryTaskId, 'PROGRESS', progress_info
                                )
                                # 进度更新不计入 updated_count，避免频繁日志
                                logger.debug(f"任务 {meeting.celeryTaskId} 进度更新，会议ID: {meeting.id}")
                            
                    except Exception as e:
                        logger.error(f"检查任务 {meeting.celeryTaskId} 状态时发生错误: {str(e)}")
                        continue
            
            if updated_count > 0:
                logger.info(f"📊 本次监控更新了 {updated_count} 个任务状态")
                
        except Exception as e:
            logger.error(f"监控待处理任务时发生错误: {str(e)}")
        finally:
            if db:
                try:
                    db.close()
                except Exception as e:
                    logger.warning(f"关闭数据库连接时发生错误: {str(e)}")
        
        return updated_count
    
    def _update_task_status_in_db(self, db: Session, task_id: str, state: str, result=None):
        """更新数据库中的任务状态"""
        try:
            from app.models import Meeting
            from app.utils import safe_format_result_for_storage
            
            # 查找对应的会议记录
            meeting = db.query(Meeting).filter(Meeting.celeryTaskId == task_id).first()
            
            if meeting:
                # 记录原状态，用于日志
                old_state = meeting.audioState
                old_task_status = meeting.celeryTaskStatus
                
                # 根据状态更新数据库字段
                if state == 'PENDING':
                    meeting.audioState = "uploaded"
                    meeting.celeryTaskStatus = "pending"
                    if result:
                        meeting.celeryTaskResult = safe_format_result_for_storage(result)
                elif state == 'STARTED':
                    meeting.audioState = "handing"
                    meeting.celeryTaskStatus = "running"
                    if result:
                        meeting.celeryTaskResult = safe_format_result_for_storage(result)
                elif state == 'PROGRESS':
                    meeting.audioState = "handing"
                    meeting.celeryTaskStatus = "running"
                    if result:
                        meeting.celeryTaskResult = safe_format_result_for_storage(result)
                elif state == 'SUCCESS':
                    meeting.audioState = "finished"
                    meeting.celeryTaskStatus = "finished"
                    meeting.status = "texted"
                    if result:
                        meeting.celeryTaskResult = safe_format_result_for_storage(result)
                        result = result["result"]
                        print(result)
                        url = result.get('audio_file_url', '')
                        if url:
                            logger.info(f"音频文件已生成，URL: {url}")
                            meeting.audioUrl = url
                            duration = result.get('audio_duration_s', 0)
                            if duration > 0:
                                meeting.audioDuration = duration
                            fileisze = int(result.get('file_size_mb', 0) * 1024 * 1024)
                            if fileisze > 0:
                                meeting.audioSize = fileisze

                        meeting.textUrl = result.get('result_url', '')

                    print("="*20)
                    print(meeting.audioUrl, meeting.textUrl)

                elif state == 'FAILURE':
                    meeting.audioState = "failed"
                    meeting.celeryTaskStatus = "failed"
                    if result:
                        meeting.celeryTaskResult = safe_format_result_for_storage(result)
                elif state == 'REVOKED':
                    meeting.audioState = "canceled"
                    meeting.celeryTaskStatus = "canceled"
                
                meeting.updatedAt = datetime.utcnow()
                db.commit()
                
                # 记录状态变化
                if old_state != meeting.audioState or old_task_status != meeting.celeryTaskStatus:
                    logger.info(f"会议 {meeting.id} 状态更新: {old_state}/{old_task_status} -> {meeting.audioState}/{meeting.celeryTaskStatus}")
                
                if state == 'SUCCESS' and len(meeting.textUrl) > 0:
                    #提交到refine
                    from app.tasks.refine_manager import refine_task_manager
                    refine_task_manager.submit_refine_task(meeting.id)
                    
            else:
                logger.warning(f"未找到任务ID为 {task_id} 的会议记录")
                
        except Exception as e:
            logger.error(f"更新任务状态时发生错误: {str(e)}")
            try:
                db.rollback()
            except:
                pass


# 全局任务监控器实例
task_monitor = TaskMonitor()

def start_task_monitor():
    """启动任务监控器"""
    task_monitor.start()

def stop_task_monitor():
    """停止任务监控器"""
    task_monitor.stop()

def get_monitor_stats():
    """获取监控器统计信息"""
    return task_monitor.get_stats() 