# utils.py
from datetime import datetime, timedelta
from jose import JWTError, jwt
from fastapi import Depends, HTTPException, status
from fastapi.security import OAuth2PasswordBearer
from typing import Optional
import httpx
from config import cfg

import hashlib
import requests
import oss2

# OAuth2 密码流的令牌URL - 更新为表单登录接口
oauth2_scheme = OAuth2PasswordBearer(tokenUrl="/v1/api/auth/login/form")

def create_access_token(data: dict, expires_delta: Optional[timedelta] = None):
    """创建JWT访问令牌"""
    to_encode = data.copy()
    if expires_delta:
        expire = datetime.utcnow() + expires_delta
    else:
        expire = datetime.utcnow() + timedelta(days=1)  # 默认1天过期
    to_encode.update({"exp": expire})
    encoded_jwt = jwt.encode(to_encode, cfg.JWT_SECRET_KEY, algorithm=cfg.JWT_ALGORITHM)
    return encoded_jwt

async def get_current_user(token: str = Depends(oauth2_scheme)):
    """验证当前用户的外部token"""
    credentials_exception = HTTPException(
        status_code=status.HTTP_401_UNAUTHORIZED,
        detail="无效的凭证",
        headers={"WWW-Authenticate": "Bearer"},
    )
    
    try:
        # 验证外部token
        UserHost = cfg.USER.HOST
        async with httpx.AsyncClient() as client:
            response = await client.get(
                f"{UserHost}/api/v1/profile", #/api/v1/profile verify-token
                headers={"Authorization": f"Bearer {token}"}
            )
        
    
        if response.status_code != 200:
            raise credentials_exception
        

        # 解析响应
        data = response.json()
        if data.get("code") != 200:
            raise credentials_exception
        
        # 从响应中获取用户ID
        user_info = data.get("data", {})
        user_id = user_info.get("id")
        
        if user_id is None:
            raise credentials_exception
        
        return user_id
        
    except Exception as e:
        raise credentials_exception

def safe_format_result_for_storage(result):
    """
    安全地格式化结果数据用于存储
    
    现在结果已经在executor中简化并上传到OSS，
    这个函数主要用于兼容性和额外的安全检查
    """
    import json
    from datetime import datetime
    import logging
    
    logger = logging.getLogger(__name__)
    
    try:
        if result is None:
            return None
        
        # 将结果转换为JSON字符串
        if isinstance(result, str):
            result_str = result
        else:
            result_str = json.dumps(result, ensure_ascii=False)
        
        # 检查长度 - 现在应该很小，因为只包含基本信息和OSS URL
        result_size_bytes = len(result_str.encode('utf-8'))
        
        # 数据库字段限制
        max_length = 60000  # 60KB
        
        if result_size_bytes <= max_length:
            # 记录存储类型
            if isinstance(result, dict) and result.get('storage_type') == 'oss':
                logger.info(f"转写结果已存储到OSS，数据库保存简化信息({result_size_bytes}字节)")
            return result_str
        else:
            # 这种情况不应该发生，因为已经简化了结果
            logger.warning(f"简化后的结果仍然过大({result_size_bytes / 1024:.1f}KB)，进行进一步处理")
            
            # 如果仍然过大，保留最核心的信息
            if isinstance(result, dict):
                minimal_result = {
                    "storage_type": result.get("storage_type", "oss"),
                    "result_url": result.get("result_url"),
                    "file_name": result.get("file_name"),
                    "speaker_segments_count": result.get("speaker_segments_count", 0),
                    "completed_at": result.get("completed_at"),
                    "truncated_reason": "结果过大，保留核心信息"
                }
                return json.dumps(minimal_result, ensure_ascii=False)
            else:
                return _truncate_string_result(result_str, max_length)
            
    except Exception as e:
        logger.error(f"格式化结果数据时发生错误: {str(e)}")
        return f"结果格式化失败: {str(e)}"

def _truncate_dict_result(result_dict, max_length):
    """智能截断字典类型的结果（保留旧函数以防需要）"""
    import json
    from datetime import datetime
    import logging
    
    logger = logging.getLogger(__name__)
    
    try:
        # 创建摘要结果
        summary_result = {
            "truncated": True,
            "truncated_at": datetime.utcnow().isoformat(),
            "original_keys": list(result_dict.keys()) if isinstance(result_dict, dict) else None
        }
        
        # 保留重要的统计信息
        important_keys = [
            'file_name', 'file_size_mb', 'statistics', 'status', 
            'progress', 'error', 'completed_at', 'duration',
            'canceled_at', 'reason',  # 添加取消相关的字段
            'result_url', 'storage_type', 'speaker_segments_count'  # OSS相关字段
        ]
        
        for key in important_keys:
            if key in result_dict:
                summary_result[key] = result_dict[key]
        
        # 如果有speaker_segments，保留前几个和统计信息
        if 'speaker_segments' in result_dict and isinstance(result_dict['speaker_segments'], list):
            segments = result_dict['speaker_segments']
            summary_result['speaker_segments_count'] = len(segments)
            summary_result['speaker_segments_preview'] = segments[:3]  # 只保留前3个
            
            # 统计说话人
            speakers = set(seg.get('speaker', 'unknown') for seg in segments)
            summary_result['speakers'] = list(speakers)
        
        # 生成最终JSON
        result_str = json.dumps(summary_result, ensure_ascii=False)
        
        # 如果还是太长，进一步截断
        if len(result_str.encode('utf-8')) > max_length:
            if 'speaker_segments_preview' in summary_result:
                summary_result['speaker_segments_preview'] = segments[:1] if 'speaker_segments' in result_dict and result_dict['speaker_segments'] else []
            result_str = json.dumps(summary_result, ensure_ascii=False)
        
        return result_str
        
    except Exception as e:
        logger.error(f"截断字典结果时发生错误: {str(e)}")
        return json.dumps({
            "truncated": True,
            "error": f"截断处理失败: {str(e)}",
            "truncated_at": datetime.utcnow().isoformat()
        }, ensure_ascii=False)

def _truncate_string_result(result_str, max_length):
    """截断字符串结果"""
    import json
    from datetime import datetime
    import logging
    
    logger = logging.getLogger(__name__)
    
    try:
        # 计算可用的字符长度（按UTF-8编码）
        available_bytes = max_length - 200  # 为截断信息预留空间
        
        # 二分查找合适的截断位置
        left, right = 0, len(result_str)
        while left < right:
            mid = (left + right + 1) // 2
            if len(result_str[:mid].encode('utf-8')) <= available_bytes:
                left = mid
            else:
                right = mid - 1
        
        truncated_str = result_str[:left]
        
        # 添加截断信息
        truncation_info = {
            "truncated": True,
            "original_length": len(result_str),
            "truncated_length": len(truncated_str),
            "truncated_at": datetime.utcnow().isoformat(),
            "content": truncated_str
        }
        
        return json.dumps(truncation_info, ensure_ascii=False)
        
    except Exception as e:
        logger.error(f"截断字符串结果时发生错误: {str(e)}")
        return json.dumps({
            "truncated": True,
            "error": f"截断处理失败: {str(e)}",
            "truncated_at": datetime.utcnow().isoformat()
        }, ensure_ascii=False)


def copy_file_to_oss(src:str,  prefix:str):
    try:
        #下载到本地，再拷贝到云端
        # 下载文件到本地
        data = requests.get(src).content
        file_hash = hashlib.md5(data).hexdigest()
        file_name = f"{file_hash}.{src.split('.')[-1]}"
        
        auth = oss2.Auth(cfg.OSS.ACCESS_KEY_ID, cfg.OSS.ACCESS_KEY_SECRET)
        bucket = oss2.Bucket(auth, cfg.OSS.ENDPOINT, cfg.OSS.BUCKET_NAME)
        
        # 生成文件名和路
        filename = f"{prefix}/{datetime.now().year}/{datetime.now().month:02d}/{file_name}"
        
        # 上传到OSS
        bucket.put_object(filename, data)
        
        # 生成文件URL
        file_url = f"https://{cfg.OSS.BUCKET_NAME}.{cfg.OSS.ENDPOINT}/{filename}"

        return file_url
    
    except Exception as e:
        raise e


import json
import oss2
from datetime import datetime
from config import cfg

def upload_result_to_oss(result_data: dict, meeting_id: int, prefix:str="transcription_results") -> str:
    """
    将转写结果上传到OSS
    
    Args:
        result_data: 转写结果数据
        meeting_id: 会议ID
        
    Returns:
        str: OSS文件URL
    """
    try:
        # 配置OSS
        auth = oss2.Auth(cfg.OSS.ACCESS_KEY_ID, cfg.OSS.ACCESS_KEY_SECRET)
        bucket = oss2.Bucket(auth, cfg.OSS.ENDPOINT, cfg.OSS.BUCKET_NAME)
        
        # 生成文件名和路径
        timestamp = int(datetime.now().timestamp()*1000)
        filename = f"{prefix}/{datetime.now().year}/{datetime.now().month:02d}/{meeting_id}_{timestamp}.json"
        
        # 将结果转换为JSON字符串
        json_content = json.dumps(result_data, ensure_ascii=False, indent=2)
        
        # 上传到OSS
        bucket.put_object(filename, json_content, headers={'Content-Type': 'application/json'})
        
        # 生成文件URL
        file_url = f"https://{cfg.OSS.BUCKET_NAME}.{cfg.OSS.ENDPOINT}/{filename}"
        
        print(f"✅ 转写结果已上传到OSS: {file_url}")
        return file_url
        
    except Exception as e:
        print(f"❌ 上传转写结果到OSS失败: {e}")
        raise

def upload_text_to_oss(text: str, meeting_id: int, prefix:str="transcription_results") -> str:
    try:
        # 配置OSS
        auth = oss2.Auth(cfg.OSS.ACCESS_KEY_ID, cfg.OSS.ACCESS_KEY_SECRET)
        bucket = oss2.Bucket(auth, cfg.OSS.ENDPOINT, cfg.OSS.BUCKET_NAME)
        
        # 生成文件名和路径
        timestamp = int(datetime.now().timestamp()*1000)
        filename = f"{prefix}/{datetime.now().year}/{datetime.now().month:02d}/{meeting_id}_{timestamp}.json"
        
        # 上传到OSS
        bucket.put_object(filename, text, headers={'Content-Type': 'application/json'})
        
        # 生成文件URL
        file_url = f"https://{cfg.OSS.BUCKET_NAME}.{cfg.OSS.ENDPOINT}/{filename}"
        
        print(f"✅ 转写结果已上传到OSS: {file_url}")
        
        return file_url
        
    except Exception as e:
        print(f"❌ 上传转写结果到OSS失败: {e}")
        raise


import json


def group_segments_by_speaker_and_size(segments, thunk_size=24*1024):   
    """
    智能分组：先按speaker连续分组，再按json字符串大小限制分组，最后将多个说话人group如果加起来小于thunk_size则合并
    :param segments: [{"id":..., "speaker":..., "text":...}, ...]
    :param thunk_size: 单组最大字节数
    :return: List[List[segment]]
    """
    groups = []
    current_group = []
    current_speaker = None

    def group_size(g):
        return len(json.dumps(g, ensure_ascii=False).encode("utf-8"))

    for seg in segments:
        if not current_group or seg["speaker"] == current_speaker:
            current_group.append(seg)
            current_speaker = seg["speaker"]
        else:
            # speaker变了，先尝试把current_group加入groups
            if group_size(current_group) > thunk_size:
                # 超过限制，需要拆分
                split_groups = split_group_by_size(current_group, thunk_size)
                groups.extend(split_groups)
            else:
                groups.append(current_group)
            current_group = [seg]
            current_speaker = seg["speaker"]

    # 处理最后一组
    if current_group:
        if group_size(current_group) > thunk_size:
            split_groups = split_group_by_size(current_group, thunk_size)
            groups.extend(split_groups)
        else:
            groups.append(current_group)

    # 新增：合并多个说话人group，只要合并后不超过thunk_size
    merged_groups = []
    temp = []
    for group in groups:
        if not temp:
            temp = group[:]
        else:
            combined = temp + group
            if group_size(combined) <= thunk_size:
                temp = combined
            else:
                merged_groups.append(temp)
                temp = group[:]
    if temp:
        merged_groups.append(temp)
    return merged_groups

def split_group_by_size(group, thunk_size):
    """
    拆分单个group，保证每个子组不超过thunk_size
    优先按speaker分离，否则按segment截断
    """
    result = []
    temp = []
    for seg in group:
        temp.append(seg)
        if len(json.dumps(temp, ensure_ascii=False).encode("utf-8")) > thunk_size:
            # 超过限制
            if len(temp) == 1:
                # 只有一个segment也超了，只能硬截断
                result.append([seg])
                temp = []
            else:
                # 去掉最后一个segment
                last = temp.pop()
                result.append(temp[:])
                temp = [last]
    if temp:
        result.append(temp)
    return result