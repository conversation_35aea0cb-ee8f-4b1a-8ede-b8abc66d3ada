services:
  meeting-mysql57:
    image: registry.cn-hangzhou.aliyuncs.com/sskw_dev/mysql:5.7
    container_name: meeting-mysql57
    restart: always

    ports:
      - "3308:3306"

    volumes:
      - ./data/mysql:/var/lib/mysql

    environment:
      - MYSQL_USER=dev1
      - MYSQL_PASSWORD=dev12025
      - MYSQL_ROOT_PASSWORD=dev12025
      - MYSQL_DATABASE=meeting-ai
      - TZ=Asia/Shanghai
      
    command: 
      - "--character-set-server=utf8mb4"
      - "--collation-server=utf8mb4_unicode_ci"

  # Redis 服务
  meeting-redis:
    image: redis:7-alpine
    container_name: meeting-redis
    ports:
      - "6379:6379"
    volumes:
      - ./data/redis:/data
    command: redis-server --appendonly yes
    restart: unless-stopped
    networks:
      - meeting_network
    environment:
      - TZ=Asia/Shanghai

  # funasr-monitor:
  #   image: funasr-monitor:0.0.1
  #   container_name: funasr-monitor
  #   restart: always
  #   networks:
  #     - meeting_network
  #   environment:
  #     - MEETING_CONF_CELERY__BROKER_URL=redis://meeting-redis:6379/0
  #     - MEETING_CONF_CELERY__RESULT_BACKEND=redis://meeting-redis:6379/1
  #     - MEETING_CONF_STORAGE__MYSQL__URI=mysql://dev1:dev12025@meeting-mysql57:3306/meeting-ai?charset=utf8mb4
  #     - TZ=Asia/Shanghai
      
  #   command: 
  #     - python 
  #     - scripts/start_monitor_worker.py

  # funasr-monitor-beat:
  #   image: funasr-monitor:0.0.1
  #   container_name: funasr-monitor-beat
  #   restart: always
  #   networks:
  #     - meeting_network
  #   environment:
  #     - MEETING_CONF_CELERY__BROKER_URL=redis://meeting-redis:6379/0
  #     - MEETING_CONF_CELERY__RESULT_BACKEND=redis://meeting-redis:6379/1
  #     - MEETING_CONF_STORAGE__MYSQL__URI=mysql://dev1:dev12025@meeting-mysql57:3306/meeting-ai?charset=utf8mb4
  #     - TZ=Asia/Shanghai

  #   command: 
  #     - python 
  #     - scripts/start_monitor_worker.py
  #     - --beat

  funasr-executor:
    image: funasr-worker:0.0.2
    container_name: funasr-executor
    restart: always
    networks:
      - meeting_network
    environment:
      - MODELSCOPE_CACHE=/workspace/models
      - MEETING_CONF_CELERY__BROKER_URL=redis://meeting-redis:6379/0
      - MEETING_CONF_CELERY__RESULT_BACKEND=redis://meeting-redis:6379/1
      - TZ=Asia/Shanghai
      - PYTORCH_CUDA_ALLOC_CONF=expandable_segments:True

    volumes:
      - D:/cache_data:/workspace/models

    command:
      - python
      - scripts/start_executor_worker.py
    deploy:
      resources:
        reservations:
          devices:
            - driver: nvidia
              device_ids: [ "0" ]
              capabilities: [ gpu ]

networks:
  meeting_network:
    driver: bridge
