import pymysql
pymysql.install_as_MySQLdb()

from config import cfg
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker
from passlib.context import CryptContext  # 用于密码哈希


# 创建数据库引擎和会话
engine = create_engine(cfg.STORAGE.MYSQL.URI)
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

# 创建密码上下文
pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")


print( cfg.STORAGE.MYSQL.URI )

# 依赖项函数 - 用于路由中获取数据库会话
def get_db():
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()