#!/usr/bin/env python3
"""
测试XML解析器是否正常工作
"""
from bs4 import BeautifulSoup

def test_xml_parser():
    """测试XML解析器"""
    try:
        # 测试简单的XML
        xml_content = """<?xml version="1.0" encoding="UTF-8"?>
        <root>
            <test>Hello World</test>
        </root>"""
        
        print("测试XML解析器...")
        soup = BeautifulSoup(xml_content, 'xml')
        print(f"✅ XML解析成功: {soup.find('test').text}")
        
        # 测试可用的解析器
        print("\n可用的解析器:")
        try:
            BeautifulSoup("<test></test>", 'xml')
            print("✅ xml 解析器可用")
        except:
            print("❌ xml 解析器不可用")
            
        try:
            BeautifulSoup("<test></test>", 'lxml')
            print("✅ lxml 解析器可用")
        except:
            print("❌ lxml 解析器不可用")
            
        try:
            BeautifulSoup("<test></test>", 'html.parser')
            print("✅ html.parser 解析器可用")
        except:
            print("❌ html.parser 解析器不可用")
            
    except Exception as e:
        print(f"❌ XML解析器测试失败: {e}")

if __name__ == "__main__":
    test_xml_parser()
