# routers/meeting.py
import asyncio
import json
from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.orm import Session
from sqlalchemy import func, or_
from typing import List, Optional, Any
from pydantic import BaseModel
from datetime import datetime, timedelta

from app.models import Meeting, HttpResult, success, error
from app.utils import get_current_user
import logging
from app.mysql import get_db


# 引入任务管理器
from app.tasks.refine_manager import refine_task_manager

# 设置日志
logger = logging.getLogger(__name__)

# 创建路由器
router = APIRouter(prefix="/v1/api/meeting", tags=["会议管理"])

# 分页响应模型
class MeetingResponse(BaseModel):
    id: int
    name: str
    users: Optional[str] = None
    meetType: Optional[str] = None
    desc: Optional[str] = None
    meetingAddress: Optional[str] = None
    audioOriginSize: Optional[int] = None
    audioDuration: Optional[int] = None
    audioUrl: Optional[str] = None
    audioSize: Optional[int] = None
    audioState: str
    status: str
    meeting_at: Optional[str] = None
    created_at: Optional[str] = None
    updated_at: Optional[str] = None

# 会议详情响应模型
class MeetingDetailResponse(BaseModel):
    id: int
    name: str
    users: Optional[str] = None
    meetType: Optional[str] = None
    desc: Optional[str] = None
    meetingAddress: Optional[str] = None
    user_id: int
    audioOriginUrls: str
    audioOriginSize: Optional[int] = None
    audioDuration: Optional[int] = None
    audioUrl: Optional[str] = None
    audioSize: Optional[int] = None
    audioState: str
    celeryTaskId: Optional[str] = None
    celeryTaskStatus: Optional[str] = None
    celeryTaskResult: Optional[str] = None
    created_at: Optional[str] = None
    updated_at: Optional[str] = None
    meeting_at: Optional[str] = None

    spkList: Optional[str] = None #说话人列表
    textUrl: Optional[str] = None #文本结果URL
    refinedTextUrl: Optional[str] = None #Ai微调后文本结果URL
    summaryUrl: Optional[str] = None #摘要结果URL
    status: str

class PaginatedMeetingResponse(BaseModel):
    meetings: List[MeetingResponse]
    total: int
    page: int
    page_size: int
    total_pages: int

# 创建会议请求模型
class CreateMeetingRequest(BaseModel):
    name: str
    meetType: Optional[str] = None
    users: Optional[str] = None
    desc: Optional[str] = None
    meetingAddress: Optional[str] = None
    audioOriginUrls: str
    audioOriginSize: Optional[int] = None
    audioDuration: Optional[int] = None
    audioUrl: Optional[str] = None
    audioSize: Optional[int] = None
    meeting_at: Optional[str] = None
    audioState: str = "uploaded"  # 默认状态

# 创建会议响应模型
class CreateMeetingResponse(BaseModel):
    id: int
    name: str
    meetType: Optional[str] = None
    users: Optional[str] = None
    desc: Optional[str] = None
    meetingAddress: Optional[str] = None
    created_at: str
    updated_at: str
    meeting_at: str
    audioState: str

# 音频转换状态响应模型
class AudioConversionStatusResponse(BaseModel):
    meeting_id: int
    audio_state: str
    task_status: Optional[str] = None
    progress: Optional[int] = None
    current_file: Optional[int] = None
    total_files: Optional[int] = None
    error_message: Optional[str] = None
    celery_task_id: Optional[str] = None
    celery_task_status: Optional[str] = None
    celery_task_result: Optional[str] = None
    estimated_completion_time: Optional[str] = None

# 获取所有会议列表
@router.get("/list", response_model=HttpResult[PaginatedMeetingResponse])
async def get_meetings(
    page: int = Query(1, ge=1, description="页码，从1开始"),
    page_size: int = Query(10, ge=1, le=100, description="每页数量，最大100"),
    keywords: Optional[str] = Query(None, description="搜索关键字模糊匹配"),
    current_user_id: int = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    '''
    获取所有会议列表（支持分页和关键字搜索）

    参数说明：
    - page: 页码，从1开始
    - page_size: 每页数量，最大100
    - keywords: 搜索关键字，支持对会议名称(name)和描述(desc)字段进行模糊匹配
    '''
    try:
        # 计算偏移量
        offset = (page - 1) * page_size

        # 构建基础查询条件
        base_filter = Meeting.user_id == current_user_id

        # 如果提供了关键字搜索，添加模糊匹配条件
        if keywords and keywords.strip():
            keyword_filter = or_(
                Meeting.name.like(f"%{keywords.strip()}%"),
                Meeting.desc.like(f"%{keywords.strip()}%")
            )
            base_filter = base_filter & keyword_filter

        # 查询总数
        total_count = db.query(func.count(Meeting.id))\
            .filter(base_filter)\
            .scalar()

        # 查询会议列表（分页）
        meetings = db.query(
            Meeting.id,
            Meeting.name,
            Meeting.users,
            Meeting.meetType,
            Meeting.desc,
            Meeting.createdAt,
            Meeting.updatedAt,
            Meeting.audioState,
            Meeting.audioOriginSize,
            Meeting.audioDuration,
            Meeting.audioUrl,
            Meeting.audioSize,
            Meeting.status,
            Meeting.meetingAt,
            Meeting.meetingAddress
        ).filter(base_filter)\
         .order_by(Meeting.createdAt.desc())\
         .offset(offset)\
         .limit(page_size)\
         .all()

        # 计算总页数
        total_pages = (total_count + page_size - 1) // page_size

        # 格式化响应数据
        meeting_list = []
        for meeting in meetings:
            meeting_list.append(MeetingResponse(
                id=meeting.id,
                name=meeting.name,
                users=meeting.users,
                meetType=meeting.meetType,
                desc=meeting.desc,
                audioOriginSize=meeting.audioOriginSize,
                audioDuration=meeting.audioDuration,
                audioUrl=meeting.audioUrl,
                audioSize=meeting.audioSize,
                audioState=meeting.audioState,
                status=meeting.status,
                meeting_at=meeting.meetingAt.isoformat() if meeting.meetingAt else None,
                meetingAddress=meeting.meetingAddress,
                created_at=meeting.createdAt.isoformat() if meeting.createdAt else None,
                updated_at=meeting.updatedAt.isoformat() if meeting.updatedAt else None
            ))

        return success(PaginatedMeetingResponse(
            meetings=meeting_list,
            total=total_count,
            page=page,
            page_size=page_size,
            total_pages=total_pages
        ))

    except Exception as e:
        logger.error(f"获取会议列表失败: {str(e)}")
        raise HTTPException(status_code=500, detail="获取会议列表失败")

# 获取会议详情
@router.get("/detail/{meeting_id}", response_model=HttpResult[MeetingDetailResponse])
async def get_meeting_detail(
    meeting_id: int,
    current_user_id: int = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    '''
    获取会议详情
    '''
    try:
        # 验证会议存在且属于当前用户
        meeting = db.query(Meeting).filter(
            Meeting.id == meeting_id,
            Meeting.user_id == current_user_id
        ).first()

        if not meeting:
            raise HTTPException(status_code=404, detail="会议不存在或无权访问")
        
        # 构造响应数据
        meeting_detail = MeetingDetailResponse(
            id=meeting.id,
            name=meeting.name,
            users=meeting.users,
            meetType=meeting.meetType,
            desc=meeting.desc,
            user_id=meeting.user_id,
            audioOriginUrls=meeting.audioOriginUrls,
            audioOriginSize=meeting.audioOriginSize,
            audioDuration=meeting.audioDuration,
            audioUrl=meeting.audioUrl,
            audioSize=meeting.audioSize,
            audioState=meeting.audioState,
            celeryTaskId=meeting.celeryTaskId,
            celeryTaskStatus=meeting.celeryTaskStatus,
            celeryTaskResult=meeting.celeryTaskResult,
            spkList=meeting.spkList,
            textUrl=meeting.textUrl,
            refinedTextUrl=meeting.refinedTextUrl,
            summaryUrl=meeting.summaryUrl,
            meetingAddress=meeting.meetingAddress,
            status=meeting.status,
            meeting_at=meeting.meetingAt.isoformat() if meeting.meetingAt else None,
            created_at=meeting.createdAt.isoformat() if meeting.createdAt else None,
            updated_at=meeting.updatedAt.isoformat() if meeting.updatedAt else None
        )

        return success(meeting_detail)

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取会议详情失败: {str(e)}")
        raise HTTPException(status_code=500, detail="获取会议详情失败")

# 创建新会议
@router.post("/create", response_model=HttpResult[CreateMeetingResponse])
async def create_meeting(
    meeting_data: CreateMeetingRequest,
    current_user_id: int = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    '''
    创建新会议
    '''
    try:
        # 验证必填字段
        if not meeting_data.name or len(meeting_data.name.strip()) == 0:
            raise HTTPException(status_code=400, detail="会议名称不能为空")
        
        if not meeting_data.audioOriginUrls or len(meeting_data.audioOriginUrls.strip()) == 0:
            raise HTTPException(status_code=400, detail="音频原始URL不能为空")
        
        if not meeting_data.meeting_at or len(meeting_data.meeting_at.strip()) == 0:
            raise HTTPException(status_code=400, detail="会议时间不能为空")

        try:
            meeting_data.meeting_at = datetime.strptime(meeting_data.meeting_at, "%Y-%m-%d %H:%M:%S")
        except:
            raise HTTPException(status_code=400, detail="会议时间格式错误")
        
        #校验audioOriginUrls是一个数组字符串能被json.loads解析
        try:
            audioOriginUrls = json.loads(meeting_data.audioOriginUrls)
            if not isinstance(audioOriginUrls, list) and len(audioOriginUrls) < 1:
                raise HTTPException(status_code=400, detail="音频原始URL格式错误")
            if len(audioOriginUrls) == 1:
                meeting_data.audioUrl = audioOriginUrls[0]
                meeting_data.audioSize = meeting_data.audioOriginSize

            elif len(meeting_data.audioUrl) < 1:
                meeting_data.audioUrl = audioOriginUrls[0]
                meeting_data.audioSize = meeting_data.audioOriginSize

        except:
            raise HTTPException(status_code=400, detail="音频原始URL格式错误")
        
        # 验证音频状态
        valid_states = ["uploaded", "handing", "finished", "canceled", "failed"]
        if meeting_data.audioState not in valid_states:
            raise HTTPException(status_code=400, detail=f"音频状态必须是以下之一: {', '.join(valid_states)}")
        
        # 创建新会议
        new_meeting = Meeting(
            name=meeting_data.name.strip(),
            meetType=meeting_data.meetType,
            users=meeting_data.users,
            desc=meeting_data.desc,
            meetingAddress=meeting_data.meetingAddress,
            user_id=current_user_id,
            audioOriginUrls=meeting_data.audioOriginUrls,
            audioOriginSize=meeting_data.audioOriginSize,
            audioDuration=meeting_data.audioDuration,
            audioUrl=meeting_data.audioUrl,
            audioSize=meeting_data.audioSize,
            audioState=meeting_data.audioState,
            meetingAt=meeting_data.meeting_at,
            status = "created"
        )
        
        db.add(new_meeting)
        db.commit()
        db.refresh(new_meeting)

        #调用音频转换celery任务
        from app.tasks.utils import startAsrTask

        try:
            # 提交任务
            audioOriginUrls = json.loads(new_meeting.audioOriginUrls)
            taskResult = startAsrTask(audioOriginUrls, new_meeting.id)
            
            if not taskResult["task_id"]:
                # 任务提交失败
                logger.error(f"Celery任务提交失败，会议ID: {new_meeting.id}")
                # 回滚数据库事务
                db.rollback()
                raise HTTPException(status_code=500, detail="音频转换任务提交失败，请稍后重试")

            print(f"任务已提交，ID: {taskResult['task_id']}")

            # 更新会议信息
            new_meeting.celeryTaskId = taskResult["task_id"]
            new_meeting.celeryTaskStatus = "pending"
            db.commit()
            db.refresh(new_meeting)
           
        except HTTPException:
            # 重新抛出HTTP异常
            raise
        except Exception as task_error:
            # 任务提交过程中的其他异常
            logger.error(f"音频转换任务提交异常，会议ID: {new_meeting.id}, 错误: {str(task_error)}")
            # 回滚数据库事务
            db.rollback()
            raise HTTPException(status_code=500, detail="音频转换任务提交失败，请稍后重试")
            
        return success(CreateMeetingResponse(
            id=new_meeting.id,
            name=new_meeting.name,
            meetType=new_meeting.meetType,
            users=new_meeting.users,
            desc=new_meeting.desc,
            meetingAddress=new_meeting.meetingAddress,
            created_at=new_meeting.createdAt.isoformat() if new_meeting.createdAt else None,
            updated_at=new_meeting.updatedAt.isoformat() if new_meeting.updatedAt else None,
            audioState=new_meeting.audioState,
            meeting_at=new_meeting.meetingAt.isoformat() if new_meeting.meetingAt else None
        ))
    
    except HTTPException:
        raise
    except Exception as e:
        db.rollback()
        logger.error(f"创建会议失败: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

# 删除会议
@router.post("/delete/{meeting_id}", response_model=HttpResult[str])
async def delete_meeting(
    meeting_id: int,
    current_user_id: int = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    '''
    删除会议
    '''
    try:
        # 验证会议存在且属于当前用户
        meeting = db.query(Meeting).filter(
            Meeting.id == meeting_id,
            Meeting.user_id == current_user_id
        ).first()

        if not meeting:
            raise HTTPException(status_code=404, detail="会议不存在或无权访问")
        
        # 删除会议
        db.delete(meeting)
        db.commit()
        
        #如果有celery任务，取消任务
        if meeting.celeryTaskId:
            try:
                from app.tasks import celery_app
                task_result = celery_app.AsyncResult(meeting.celeryTaskId)
                
                if task_result.state in ['PENDING', 'STARTED', 'PROGRESS']:
                    # 取消任务
                    task_result.revoke(terminate=True)
                    logger.info(f"取消任务: {meeting.celeryTaskId}")
                else:
                    logger.warning(f"任务状态为 {task_result.state}，无法取消")
                    
            except Exception as e:
                logger.warning(f"取消Celery任务失败: {str(e)}")


        return success("会议已删除")
        
    except HTTPException:

        raise
    except Exception as e:
        db.rollback()
        logger.error(f"删除会议时发生错误: {str(e)}")
        raise HTTPException(status_code=500, detail="删除会议失败")

# 更新会议请求模型
class UpdateMeetingRequest(BaseModel):
    name: Optional[str] = None
    meetType: Optional[str] = None
    users: Optional[str] = None
    desc: Optional[str] = None
    spkList: Optional[str] = None
    textUrl: Optional[str] = None
    refinedTextUrl: Optional[str] = None
    summaryUrl: Optional[str] = None
    meetingAddress: Optional[str] = None

# 更新会议响应模型
class UpdateMeetingResponse(BaseModel):
    id: int
    name: str
    meetType: Optional[str] = None
    users: Optional[str] = None
    desc: Optional[str] = None
    spkList: Optional[str] = None
    textUrl: Optional[str] = None
    refinedTextUrl: Optional[str] = None
    summaryUrl: Optional[str] = None
    meetingAddress: Optional[str] = None
    created_at: str
    updated_at: str

# 更新会议
@router.post("/update/{meeting_id}", response_model=HttpResult[UpdateMeetingResponse])
async def update_meeting(
    meeting_id: int,
    meeting_data: UpdateMeetingRequest,
    current_user_id: int = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    '''
    更新会议信息
    '''
    try:
        # 验证会议存在且属于当前用户
        meeting = db.query(Meeting).filter(
            Meeting.id == meeting_id,
            Meeting.user_id == current_user_id
        ).first()

        if not meeting:
            raise HTTPException(status_code=404, detail="会议不存在或无权访问")
        
        # 更新会议信息
        if meeting_data.name is not None:
            if len(meeting_data.name.strip()) == 0:
                raise HTTPException(status_code=400, detail="会议名称不能为空")
            meeting.name = meeting_data.name.strip()
        
        if meeting_data.meetType is not None:
            meeting.meetType = meeting_data.meetType
        
        if meeting_data.users is not None:
            meeting.users = meeting_data.users
        
        if meeting_data.desc is not None:
            meeting.desc = meeting_data.desc
        
        if meeting_data.meetingAddress is not None:
            meeting.meetingAddress = meeting_data.meetingAddress
        
        if meeting_data.spkList is not None:
            meeting.spkList = meeting_data.spkList
        
        if meeting_data.textUrl is not None:
            meeting.textUrl = meeting_data.textUrl
        
        if meeting_data.refinedTextUrl is not None:
            meeting.refinedTextUrl = meeting_data.refinedTextUrl
        
        if meeting_data.summaryUrl  is not None:
            meeting.summaryUrl = meeting_data.summaryUrl
        
        # 更新修改时间
        meeting.updatedAt = datetime.utcnow()
        
        db.commit()
        db.refresh(meeting)

        return success(UpdateMeetingResponse(
            id=meeting.id,
            name=meeting.name,
            meetType=meeting.meetType,
            users=meeting.users,
            desc=meeting.desc,
            meetingAddress=meeting.meetingAddress,
            spkList=meeting.spkList,
            textUrl=meeting.textUrl,
            refinedTextUrl=meeting.refinedTextUrl,
            summaryUrl=meeting.summaryUrl,
            created_at=meeting.createdAt.isoformat() if meeting.createdAt else None,
            updated_at=meeting.updatedAt.isoformat() if meeting.updatedAt else None
        ))

    except HTTPException:
        raise
    except Exception as e:
        db.rollback()
        logger.error(f"更新会议时发生错误: {str(e)}")
        raise HTTPException(status_code=500, detail="更新会议失败")

# 任务重试请求模型
class RetryTaskRequest(BaseModel):
    force: bool = False  # 是否强制重试

# 任务取消请求模型
class CancelTaskRequest(BaseModel):
    reason: Optional[str] = None

# 重试失败的任务
@router.post("/retry-task/{meeting_id}", response_model=HttpResult[str])
async def retry_failed_task(
    meeting_id: int,
    retry_data: RetryTaskRequest,
    current_user_id: int = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    '''
    重试失败的任务
    '''
    try:
        # 验证会议存在且属于当前用户
        meeting = db.query(Meeting).filter(
            Meeting.id == meeting_id,
            Meeting.user_id == current_user_id
        ).first()

        if not meeting:
            raise HTTPException(status_code=404, detail="会议不存在或无权访问")
        
        # 检查任务状态
        # if meeting.audioState not in ["failed", "canceled"]:
        #     if not retry_data.force:
        #         raise HTTPException(status_code=400, detail="只有失败或已取消的任务可以重试")
        
        # 检查是否有正在运行的任务
        if meeting.audioState == "handing" and meeting.celeryTaskId:
            try:
                from app.tasks import celery_app
                task_result = celery_app.AsyncResult(meeting.celeryTaskId)
                print("检查任务状态", task_result.state)
                
                if task_result.state in ['PENDING', 'STARTED', 'PROGRESS']:
                    if not retry_data.force:
                        raise HTTPException(status_code=400, detail="任务正在运行中，无法重试")
                    else:
                        # 强制取消当前任务
                        task_result.revoke(terminate=True)
                        logger.info(f"强制取消任务: {meeting.celeryTaskId}")
            except Exception as e:
                logger.warning(f"检查任务状态失败: {str(e)}")
        
        # 重新提交任务
        try:
            from app.tasks.executor import transcribe_audio_task
            
            # 解析音频原始URL
            audio_origin_urls = json.loads(meeting.audioOriginUrls)
            
            # 提交新任务
            task = transcribe_audio_task.delay(meeting.id, audio_origin_urls)
            
            if not task or not task.id:
                raise HTTPException(status_code=500, detail="任务提交失败")
            
            # 更新会议信息
            meeting.celeryTaskId = task.id
            meeting.celeryTaskStatus = "pending"
            meeting.audioState = "handing"
            meeting.updatedAt = datetime.utcnow()
            
            db.commit()
            
            logger.info(f"会议 {meeting_id} 任务重试成功，新任务ID: {task.id}")
            
            return success(f"任务重试成功，新任务ID: {task.id}")
            
        except Exception as e:
            logger.error(f"重试任务失败: {str(e)}")
            raise HTTPException(status_code=500, detail="任务重试失败")
        
    except HTTPException:
        raise
    except Exception as e:
        db.rollback()
        logger.error(f"重试任务时发生错误: {str(e)}")
        raise HTTPException(status_code=500, detail="重试任务失败")

# # 取消正在运行的任务
# @router.post("/cancel-task/{meeting_id}", response_model=HttpResult[str])
# async def cancel_running_task(
#     meeting_id: int,
#     cancel_data: CancelTaskRequest,
#     current_user_id: int = Depends(get_current_user),
#     db: Session = Depends(get_db)
# ):
#     '''
#     取消正在运行的任务
#     '''
#     try:
#         # 验证会议存在且属于当前用户
#         meeting = db.query(Meeting).filter(
#             Meeting.id == meeting_id,
#             Meeting.user_id == current_user_id
#         ).first()

#         if not meeting:
#             raise HTTPException(status_code=404, detail="会议不存在或无权访问")
        
#         # 检查任务状态
#         if meeting.audioState not in ["handing", "uploaded"]:
#             raise HTTPException(status_code=400, detail="只有正在处理或已上传的任务可以取消")
        
#         # 取消Celery任务
#         if meeting.celeryTaskId:
#             try:
#                 from app.tasks import celery_app
#                 task_result = celery_app.AsyncResult(meeting.celeryTaskId)
                
#                 if task_result.state in ['PENDING', 'STARTED', 'PROGRESS']:
#                     # 取消任务
#                     task_result.revoke(terminate=True)
#                     logger.info(f"取消任务: {meeting.celeryTaskId}")
#                 else:
#                     logger.warning(f"任务状态为 {task_result.state}，无法取消")
                    
#             except Exception as e:
#                 logger.warning(f"取消Celery任务失败: {str(e)}")
        
#         # 更新会议状态
#         meeting.audioState = "canceled"
#         meeting.celeryTaskStatus = "canceled"
#         meeting.celeryTaskResult = safe_format_result_for_storage({
#             "canceled_at": datetime.utcnow().isoformat(),
#             "reason": cancel_data.reason or "用户手动取消"
#         })
#         meeting.updatedAt = datetime.utcnow()
        
#         db.commit()
        
#         logger.info(f"会议 {meeting_id} 任务已取消")
        
#         return success("任务已取消")
        
#     except HTTPException:
#         raise
#     except Exception as e:
#         db.rollback()
#         logger.error(f"取消任务时发生错误: {str(e)}")
#         raise HTTPException(status_code=500, detail="取消任务失败")

# 转写结果摘要响应模型
class TranscriptionSummaryResponse(BaseModel):
    file_name: str
    file_size_mb: float
    audio_duration_seconds: Optional[float] = None
    speaker_segments_count: int
    speakers: List[str]
    completed_at: str
    storage_type: str
    result_url: str

# 健壮地处理AI返回的各种格式，抽离出JSON数组
def extract_json_array(text):
    """
    从AI返回的文本中提取JSON数组
    支持多种格式：纯JSON、markdown代码块、混合文本等
    """
    if not text or not isinstance(text, str):
        raise ValueError("返回内容为空或格式错误")
    
    # 去除首尾空白字符
    text = text.strip()
    
    # 1. 处理markdown代码块格式
    if "```json" in text:
        # 提取json代码块内容
        start_markers = ["```json", "```JSON"]
        end_marker = "```"
        
        for start_marker in start_markers:
            if start_marker in text:
                start_idx = text.find(start_marker) + len(start_marker)
                end_idx = text.find(end_marker, start_idx)
                if end_idx != -1:
                    text = text[start_idx:end_idx].strip()
                    break
    
    # 2. 处理其他代码块格式
    elif "```" in text:
        # 提取普通代码块内容
        parts = text.split("```")
        if len(parts) >= 3:
            # 取第二部分（代码块内容）
            text = parts[1].strip()
            # 如果第一行是语言标识符，去掉它
            lines = text.split('\n')
            if lines and lines[0].lower() in ['json', 'javascript', 'js']:
                text = '\n'.join(lines[1:]).strip()
    
    # 3. 查找JSON数组的开始和结束位置
    start_idx = -1
    end_idx = -1
    
    # 查找数组开始位置
    for i, char in enumerate(text):
        if char == '[':
            start_idx = i
            break
    
    # 如果没找到数组开始，尝试查找对象开始
    if start_idx == -1:
        for i, char in enumerate(text):
            if char == '{':
                start_idx = i
                break
    
    if start_idx == -1:
        raise ValueError("未找到JSON数据的开始位置")
    
    # 查找对应的结束位置
    bracket_count = 0
    start_char = text[start_idx]
    end_char = ']' if start_char == '[' else '}'
    
    for i in range(start_idx, len(text)):
        char = text[i]
        if char == start_char:
            bracket_count += 1
        elif char == end_char:
            bracket_count -= 1
            if bracket_count == 0:
                end_idx = i
                break
    
    if end_idx == -1:
        raise ValueError("未找到JSON数据的结束位置")
    
    # 提取JSON字符串
    json_str = text[start_idx:end_idx + 1]
    
    # 4. 解析JSON
    try:
        parsed_data = json.loads(json_str)
        
        # 确保返回的是数组
        if isinstance(parsed_data, list):
            return parsed_data
        elif isinstance(parsed_data, dict):
            # 如果是单个对象，包装成数组
            return [parsed_data]
        else:
            raise ValueError("解析出的数据不是对象或数组")
            
    except json.JSONDecodeError as e:
        raise ValueError(f"JSON解析失败: {str(e)}")

# AI优化任务响应模型
class RefineTaskResponse(BaseModel):
    task_id: int
    meeting_id: int
    status: str
    message: str

# AI优化任务状态响应模型
class RefineTaskStatusResponse(BaseModel):
    meeting_id: int
    status: str  # starting, running, processing, completed, failed, canceled
    progress: int  # 0-100
    current_group: int
    total_groups: int
    started_at: Optional[str] = None
    completed_at: Optional[str] = None
    failed_at: Optional[str] = None
    refined_url: Optional[str] = None
    error: Optional[str] = None

# # 启动AI优化任务
# @router.post("/refine-text/{meeting_id}", response_model=HttpResult[RefineTaskResponse])
# async def start_refine_task(
#     meeting_id: int,
#     current_user_id: int = Depends(get_current_user),
#     db: Session = Depends(get_db)
# ):
#     """
#     启动AI优化任务（异步执行）
#     """
#     try:
#         # 获取会议信息
#         meeting = db.query(Meeting).filter(
#             Meeting.id == meeting_id,
#             Meeting.user_id == current_user_id
#         ).first()
        
#         if not meeting:
#             raise HTTPException(status_code=404, detail="会议不存在")
        
#         # 检查任务状态
#         if meeting.audioState != "finished" or meeting.textUrl is None:
#             raise HTTPException(status_code=400, detail="转写任务尚未完成")
        
#         # 检查是否已经在优化中或已完成
#         if meeting.status == "refining":
#             # 检查任务管理器中的状态
#             task_status = refine_task_manager.get_task_status(meeting_id)
#             if task_status:
#                 raise HTTPException(status_code=400, detail="AI优化任务正在执行中")
        
#         # 提交任务到任务管理器
#         success_submitted = refine_task_manager.submit_refine_task(meeting_id)
        
#         if not success_submitted:
#             raise HTTPException(status_code=500, detail="任务提交失败，可能已达到最大并发数")
        
#         logger.info(f"已提交会议 {meeting_id} 的AI优化任务")
        
#         return success(RefineTaskResponse(
#             task_id=meeting_id,  # 使用meeting_id作为task_id
#             meeting_id=meeting_id,
#             status="submitted",
#             message="AI优化任务已提交，正在后台处理"
#         ))
        
#     except HTTPException:
#         raise
#     except Exception as e:
#         logger.error(f"启动AI优化任务失败: {str(e)}")
#         raise HTTPException(status_code=500, detail="启动AI优化任务失败")

# # 查询AI优化任务状态
# @router.get("/refine-status/{meeting_id}", response_model=HttpResult[RefineTaskStatusResponse])
# async def get_refine_task_status(
#     meeting_id: int,
#     current_user_id: int = Depends(get_current_user),
#     db: Session = Depends(get_db)
# ):
#     """
#     查询AI优化任务状态
#     """
#     try:
#         # 验证会议存在且属于当前用户
#         meeting = db.query(Meeting).filter(
#             Meeting.id == meeting_id,
#             Meeting.user_id == current_user_id
#         ).first()

#         if not meeting:
#             raise HTTPException(status_code=404, detail="会议不存在或无权访问")
        
#         # 从任务管理器获取实时状态
#         task_status = refine_task_manager.get_task_status(meeting_id)
        
#         if task_status:
#             # 任务管理器中有状态信息
#             response = RefineTaskStatusResponse(
#                 meeting_id=meeting_id,
#                 status=task_status.get("status", "unknown"),
#                 progress=task_status.get("progress", 0),
#                 current_group=task_status.get("current_group", 0),
#                 total_groups=task_status.get("total_groups", 0),
#                 started_at=task_status.get("started_at"),
#                 completed_at=task_status.get("completed_at"),
#                 failed_at=task_status.get("failed_at"),
#                 refined_url=task_status.get("refined_url"),
#                 error=task_status.get("error")
#             )
#         else:
#             # 任务管理器中没有状态，根据数据库状态判断
#             if meeting.status == "refined":
#                 response = RefineTaskStatusResponse(
#                     meeting_id=meeting_id,
#                     status="completed",
#                     progress=100,
#                     current_group=0,
#                     total_groups=0,
#                     refined_url=meeting.refinedTextUrl
#                 )
#             elif meeting.status == "refining":
#                 response = RefineTaskStatusResponse(
#                     meeting_id=meeting_id,
#                     status="pending",  # 可能是重启后还未恢复的任务
#                     progress=0,
#                     current_group=0,
#                     total_groups=0
#                 )
#             elif meeting.status == "failed":
#                 response = RefineTaskStatusResponse(
#                     meeting_id=meeting_id,
#                     status="failed",
#                     progress=0,
#                     current_group=0,
#                     total_groups=0,
#                     error="任务执行失败"
#                 )
#             else:
#                 response = RefineTaskStatusResponse(
#                     meeting_id=meeting_id,
#                     status="not_started",
#                     progress=0,
#                     current_group=0,
#                     total_groups=0
#                 )
        
#         return success(response)
        
#     except HTTPException:
#         raise
#     except Exception as e:
#         logger.error(f"查询AI优化任务状态失败: {str(e)}")
#         raise HTTPException(status_code=500, detail="查询AI优化任务状态失败")

# # 取消AI优化任务
# @router.post("/refine-cancel/{meeting_id}", response_model=HttpResult[str])
# async def cancel_refine_task(
#     meeting_id: int,
#     current_user_id: int = Depends(get_current_user),
#     db: Session = Depends(get_db)
# ):
#     """
#     取消AI优化任务
#     """
#     try:
#         # 验证会议存在且属于当前用户
#         meeting = db.query(Meeting).filter(
#             Meeting.id == meeting_id,
#             Meeting.user_id == current_user_id
#         ).first()

#         if not meeting:
#             raise HTTPException(status_code=404, detail="会议不存在或无权访问")
        
#         # 检查任务状态
#         if meeting.status != "refining":
#             raise HTTPException(status_code=400, detail="没有正在执行的AI优化任务")
        
#         # 取消任务
#         success_canceled = refine_task_manager.cancel_task(meeting_id)
        
#         if success_canceled:
#             logger.info(f"已取消会议 {meeting_id} 的AI优化任务")
#             return success("AI优化任务已取消")
#         else:
#             raise HTTPException(status_code=400, detail="任务取消失败，可能任务已完成")
        
#     except HTTPException:
#         raise
#     except Exception as e:
#         logger.error(f"取消AI优化任务失败: {str(e)}")
#         raise HTTPException(status_code=500, detail="取消AI优化任务失败")

# # 获取AI优化任务管理器状态
# @router.get("/refine-manager-status", response_model=HttpResult[dict])
# async def get_refine_manager_status(
#     current_user_id: int = Depends(get_current_user),
# ):
#     """
#     获取AI优化任务管理器状态（调试用）
#     """
#     try:
#         status_info = {
#             "is_running": refine_task_manager.is_running,
#             "running_tasks_count": len(refine_task_manager.running_tasks),
#             "max_workers": refine_task_manager.max_workers,
#             "running_task_ids": list(refine_task_manager.running_tasks.keys()),
#             "task_status_count": len(refine_task_manager.task_status)
#         }
        
#         return success(status_info)
        
#     except Exception as e:
#         logger.error(f"获取任务管理器状态失败: {str(e)}")
#         raise HTTPException(status_code=500, detail="获取任务管理器状态失败")