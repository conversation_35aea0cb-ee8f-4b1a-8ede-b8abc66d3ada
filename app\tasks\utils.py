import requests
from  config import cfg


def requetTask(api:str, queue:str, inputs):
    host = cfg.CELERY.TASKHUB_HOST
    payload = {
        "queue": queue,
        "appId": cfg.CELERY.TASK_APP_ID,
        "inputs": inputs
    }
    response = requests.post(f"{host}{api}", json=payload)
    
    print("payload=>", payload)
    print(response)

    # 检查响应状态
    if response.status_code == 200:
        result = response.json()
        return {
            "success": True,
            "task_id": result.get("task_id"),
            "error": ""
        }
    
    return {
        "success": False,
        "error": f"HTTP {response.status_code}: {response.text}",
        "task_id": None
    }


def startAsrTask(urls: list[str], meetingId: str)->dict:

    return requetTask(cfg.CELERY.TASKHUB_ASR_API, cfg.CELERY.TASK_ASR_QUEUE, {
            "audio_urls": urls,
            "data_id": f"{meetingId}"
        })

def startRefineTask(textUrl: str, reftextUrl: str, id:str):

    host = cfg.CELERY.TASKHUB_HOST
    asrapi = cfg.CELERY.TASKHUB_REFINE_API

    return requetTask(cfg.CELERY.TASKHUB_REFINE_API, cfg.CELERY.TASK_REFINE_QUEUE, {
            "textUrl": textUrl,
            "ragTextUrl": reftextUrl,
            "data_id": f"{id}"
        })