from sqlalchemy import Column, Integer, String, Text, DateTime, ForeignKey, Enum, Index
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import relationship
from datetime import datetime
from enum import Enum as PyEnum

from pydantic import BaseModel
from typing import Generic, TypeVar, Optional
from app.mysql import pwd_context

T = TypeVar('T')  # 泛型类型

class HttpResult(BaseModel, Generic[T]):
    code: int = 200    # 状态码
    message: str       # 返回消息
    data: Optional[T]  # 实际数据（可选）

def success(data: T) -> HttpResult[T]:
    return HttpResult(
        code=200,
        message="",
        data=data
    )

def error(message: str, code:int=500) -> HttpResult[None]:
    return HttpResult(
        code=code,
        message=message,
        data=None
    )


# 创建基础类
Base = declarative_base()


class User(Base):
    __tablename__ = "user"
    
    id = Column(Integer, primary_key=True)
    username = Column(String(80), unique=True, nullable=False)
    password = Column(String(200), nullable=False)
    
    # 关系
    conversations = relationship("Conversation", back_populates="user", cascade="all, delete-orphan")
    
    def set_password(self, password):
        self.password = pwd_context.hash(password)
    
    def check_password(self, password):
        return pwd_context.verify(password, self.password)

class Conversation(Base):
    __tablename__ = "conversation"
    
    id = Column(Integer, primary_key=True)
    user_id = Column(Integer, ForeignKey("user.id"), nullable=False, index=True)
    title = Column(String(255), nullable=False, default="新对话")
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # 关系
    user = relationship("User", back_populates="conversations")
    messages = relationship("Message", back_populates="conversation", cascade="all, delete-orphan")
    
    # 索引
    __table_args__ = (
        Index("idx_user_updated", user_id, updated_at.desc()),
    )

class Message(Base):
    __tablename__ = "message"
    
    id = Column(Integer, primary_key=True)
    conversation_id = Column(Integer, ForeignKey("conversation.id"), nullable=False, index=True)
    role = Column(Enum("user", "assistant"), nullable=False)
    content = Column(Text, nullable=False)
    reasoning_content = Column(Text)
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)  # 新增字段
    
    # 关系
    conversation = relationship("Conversation", back_populates="messages")
    
    # 索引
    __table_args__ = (
        Index("idx_conv_created", conversation_id, created_at),
    )

class Meeting(Base):
    __tablename__ = "meetings"
    
    id = Column(Integer, primary_key=True)

    name = Column(String(80), nullable=False)
    meetType = Column(String(80))
    users = Column(Text)
    desc = Column(Text)
    user_id = Column(Integer, nullable=False, index=True)

    meetingAddress = Column(String(255))
    meetingAt = Column(DateTime, default=datetime.utcnow) #会议时间
    createdAt = Column(DateTime, default=datetime.utcnow)
    updatedAt = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)  # 新增字段
    
    audioOriginUrls = Column(Text, nullable=False)
    audioOriginSize = Column(Integer) #会议大小
    
    audioDuration = Column(Integer) #会议时长
    audioUrl = Column(String(255))
    audioSize = Column(Integer) #会议大小
    audioState = Column(Enum("uploaded", "handing", "finished", "canceled", "failed"), nullable=False) #会议状态

    celeryTaskId = Column(String(255)) #celery任务id
    celeryTaskStatus = Column(Enum("pending", "running", "finished", "failed", "canceled")) #celery任务状态
    celeryTaskResult = Column(Text) #celery任务结果
    
    spkList = Column(Text) #说话人列表
    textUrl = Column(String(255)) #文本结果URL
    refinedTextUrl = Column(String(255)) #Ai微调后文本结果URL
    summaryUrl = Column(String(255)) #摘要结果URL
    status = Column(Enum("created", "texted","refining", "refined", "summarizing", "summarized", "failed"), nullable=False) #处理状态


class MeetingTextRefineTask(Base):
    __tablename__ = "meeting_text_refine_tasks"
    
    id = Column(Integer, primary_key=True)
    versionName = Column(String(32)) #版本
    meeting_id = Column(Integer, ForeignKey("meetings.id"), nullable=False, index=True)
    textUrl = Column(String(255)) #文本URL
    refTextUrl = Column(String(255)) #
    resultUrl = Column(String(255)) #Ai微调后文本结果URL
    status = Column(Enum("created","started", "success", "failed", "canceled"), nullable=False) #处理状态
    createdAt = Column(DateTime, default=datetime.utcnow)
    updatedAt = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow) # 新增字段
    
    taskId = Column(String(255))

class MeetingSummeryTask(Base):
    __tablename__ = "meeting_summery_tasks"
    id = Column(Integer, primary_key=True)
    versionName = Column(String(32)) #版本
    meeting_id = Column(Integer, ForeignKey("meetings.id"), nullable=False, index=True)
    textUrl = Column(String(255)) #文本L
    refTextUrl = Column(String(255)) #Ai微调后文本结果URL
    resultUrl = Column(String(255)) #Ai微调后文本结果URL
    status = Column(Enum("created","started", "success", "failed"), nullable=False) #处理状态
    createdAt = Column(DateTime, default=datetime.utcnow)
    updatedAt = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow) # 新增字段
    

class LoginToken(BaseModel):
    token: str      # 状态码
    username: str   # 返回消息

class UserProfile(BaseModel):
    username: str   # 返回消息


# 定义一个字符串的枚举
class TaskStatus(PyEnum):
    SUCCESS = "SUCCESS"
    FAILURE = "FAILURE"
