#!/usr/bin/env python3
"""
检查DOCX文件结构的脚本
"""
import zipfile
import os

def check_docx_structure(docx_path):
    """检查DOCX文件的内部结构"""
    if not os.path.exists(docx_path):
        print(f"文件不存在: {docx_path}")
        return
    
    try:
        with zipfile.ZipFile(docx_path, 'r') as zip_ref:
            print(f"DOCX文件: {docx_path}")
            print("=" * 50)
            print("文件列表:")
            for filename in sorted(zip_ref.namelist()):
                print(f"  {filename}")
            
            print("\n" + "=" * 50)
            print("查找主文档文件:")
            
            # 检查常见的文档文件名
            possible_names = [
                'word/document.xml',
                'word/documents.xml',
                'word/content.xml'
            ]
            
            for name in possible_names:
                if name in zip_ref.namelist():
                    print(f"✅ 找到: {name}")
                else:
                    print(f"❌ 未找到: {name}")
                    
    except Exception as e:
        print(f"读取DOCX文件时出错: {e}")

if __name__ == "__main__":
    check_docx_structure("static/text.docx")
